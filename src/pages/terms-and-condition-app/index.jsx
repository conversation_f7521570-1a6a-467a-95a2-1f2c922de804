import React from 'react';
import Form from '../../components/terms-and-condition/Form';
import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import { ThreeDots } from "react-loader-spinner";
import PdfViewer from '../../components/PdfViewer';

function Index() {
  const router = useRouter();
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
  const [pdfUrl, setPdfUrl] = useState(null);
  const [homepage, setHomepage] = useState(true);
  const [hideLoader, setHideLoader] = useState(false);
  const [pageNo, setPageNo] = useState(0);

  useEffect(() => {
    if (!router.isReady) return;

    let { brand, country, language, hideLoader: hideLoaderParam, pdf: getPdfParam, page } = router.query;
    if (page) {
      setPageNo(page);
    }
    if (hideLoaderParam === 'true') {
      setHideLoader(true);
    }

    if (!country || !language || !brand) {
      setHomepage(true);
      return;
    }
    setHomepage(false);

    const fetchConfigAndSetPdf = async (pdf) => {
      try {
        const response = await fetch(`${baseUrl}/terms-and-condition-app/assets/config.json`);
        const data = await response.json();

        if (brand === null) {
          router.push("/terms-and-condition-app");
          return;
        }
        if (data.brands[brand][country] === undefined) {
          router.push("/terms-and-condition-app");
          return;
        }
        const languages = data.brands[brand][country].languages;

        if (!languages.includes(language)) {
          router.push("/terms-and-condition-app");
          return;
        }

        const filename = `${brand.toUpperCase()}_${country.toUpperCase()}_${language.toLowerCase()}.pdf`;
        const redirectUrl = `${baseUrl}/terms-and-condition-app/pdf/${filename}`;
        setPdfUrl(redirectUrl);
        if (pdf) {
          router.replace(redirectUrl)
        }
      } catch (err) {
        console.error(err);
        setHomepage(true);
        return;
      }
    };

    fetchConfigAndSetPdf(getPdfParam);
  }, [router.isReady, router.query]);

  if(homepage && router.isReady) return (
    <div className="relative min-h-screen flex">
      <div className="relative w-2/3 hidden md:block">
        <img
          src={`${baseUrl}/assets/background.png`}
          alt="Background"
          className="object-cover w-full h-full" 
        />
      </div>
      <div className="w-full md:w-1/3 flex items-center justify-center z-10 bg-white">
        <Form hideLoader={hideLoader} />
      </div>

    </div>
  );
  return (
    <div className="w-full h-screen">
      {pdfUrl ? <PdfViewer pdfUrl={pdfUrl} hideLoader={hideLoader} pageno={pageNo} /> : (
        <>
          {router.isReady && !hideLoader && (
            <div className="w-full h-screen flex items-center justify-center text-3xl bg-white text-black">
              <ThreeDots
                visible={true}
                height="80"
                width="80"
                color="#4766FF"
                radius="9"
                ariaLabel="three-dots-loading"
                wrapperStyle={{}}
                wrapperClass=""
              />
            </div>
          )}
        </>
      )}
    </div>
  );
}

export default Index;
