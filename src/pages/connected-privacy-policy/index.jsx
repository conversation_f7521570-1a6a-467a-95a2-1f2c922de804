'use client';
import React from 'react';
import Form from '../../components/connected-privacy-policy/Form';
import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import { ThreeDots } from "react-loader-spinner";
import PdfViewer from '../../components/PdfViewer';

function Index() {
  const router = useRouter();
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || '';
  const [pdfUrl, setPdfUrl] = useState(null);
  const [homepage, setHomepage] = useState(true);
  const [hideLoader, setHideLoader] = useState(false);

  useEffect(() => {
    if (!router.isReady) return;

    let { country, language, hideLoader: hideLoaderParam, pdf: getPdfParam } = router.query;
    if (hideLoaderParam === 'true') {
      setHideLoader(true);
    }
    
    if (!country || !language) {
      setHomepage(true);
      return;
    }
    setHomepage(false);

    const fetchConfigAndSetPdf = async (pdf=false) => {
      try {
        const response = await fetch(`${baseUrl}/connected-vehicle-privacy-policy/assets/config.json`);
        const data = await response.json();
        const EEA = data.EEA;

        const regions = {};
        for (const [regionCode, regionData] of Object.entries(data.regions)) {
          regions[regionCode] = Object.entries(regionData)
            .filter(
              ([key, value]) => key !== "name" && typeof value === "object"
            )
            .map(([key]) => key);
        }

        if (EEA.includes(country)) {
          country = "EEA";
        }

        function getRegionFromCountry(code) {
          for (const [region, countries] of Object.entries(regions)) {
            if (Array.isArray(countries) && countries.includes(code)) {
              return region;
            }
          }
          return null;
        }

        const region = getRegionFromCountry(country);

        if (region === null) {
          router.push("/");
          return;
        }
        if (data.regions[region][country] === undefined) {
          router.push("/");
          return;
        }
        const languages = data.regions[region][country].languages;

        if (!languages.includes(language)) {
          router.push("/");
          return;
        }

        const filename = `${region}_${country}_${language}.pdf`;
        const redirectUrl = `${baseUrl}/connected-vehicle-privacy-policy/pdf/${filename}`;
        setPdfUrl(redirectUrl); // ← set the PDF URL for display
        if (pdf) {
          router.replace(redirectUrl)
        }
      } catch (err) {
        console.error(err);
        setHomepage(true);
        return;
      }
    };

    fetchConfigAndSetPdf(getPdfParam);
  }, [router.isReady, router.query]);

  if(homepage && router.isReady) return (
    <div className="relative min-h-screen flex">
      <div className="relative w-2/3 hidden md:block">
        <img
          src={`${baseUrl}/assets/background.png`}
          alt="Background"
          className="object-cover w-full h-full" 
        />
      </div>
      <div className="w-full md:w-1/3 flex items-center justify-center z-10 bg-white">
        <Form hideLoader={hideLoader} />
      </div>

    </div>
  );

  return (
    <div className="w-full h-screen">
      {pdfUrl ? <PdfViewer pdfUrl={pdfUrl} hideLoader={hideLoader} /> : (
        <>
          {router.isReady && !hideLoader && (
            <div className="w-full h-screen flex items-center justify-center text-3xl bg-white text-black">
              <ThreeDots
                visible={true}
                height="80"
                width="80"
                color="#4766FF"
                radius="9"
                ariaLabel="three-dots-loading"
                wrapperStyle={{}}
                wrapperClass=""
              />
            </div>
          )}
        </>
      )}
    </div>
  );
}

export default Index;
