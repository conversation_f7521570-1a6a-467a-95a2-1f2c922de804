import React from "react";

function XP({ translate, brand, brandConfig }) {
  if (!brandConfig) {
    return(
      <>
        <h1 className="text-2xl font-bold text-red-700 flex items-center justify-center">{translate("rtn_error_msg")}</h1>
      </>
    )
  }
  const phoneNo = brandConfig.translatable == "1" ? brandConfig.phoneNoTR : brandConfig.phoneNo;
  if (!phoneNo) {
    return(
      <>
        <h1 className="text-2xl font-bold text-red-700 flex items-center justify-center">{translate("rtn_error_msg")}</h1>
      </>
    )
  }
  return (
    <>
      <div className="max-w-2xl mx-auto text-black">
        <div className="space-y-8">
          <p className="text-xl mb-5 mt-7">
            {translate("rtn_step3_title_xp").replace("$Brand", brand)}
          </p>

          <a
            href={`tel:${phoneNo}`}
            className="text-blue-600 p-0 h-auto font-normal"
          >
            {translate("rtn_step3_website_link2").replace("$Brand", brand)}
          </a>
        </div>
      </div>
    </>
  );
}

export default XP;
