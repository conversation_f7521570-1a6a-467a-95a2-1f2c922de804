import React from 'react'
import BrandCard from './BrandCard'

function Step2({ translate, setBrand, choosenBrand }) {
  const brands = ["ac","ar","ap","cy","dg","ds","ft","je","la","op","ra"]
  return (
    <>
    <div className='text-sm text-stone-800'>
      {translate('rtn_step2')}
    </div>

    <div className='flex flex-wrap justify-center'>
      {brands.map((brand) => (
        <BrandCard key={brand} setBrand={setBrand} logoSrc={`/BrandLogos/${brand}.png`} altText={brand} isSelected={brand === choosenBrand} />
      ))}
    </div>
    </>

  )
}

export default Step2